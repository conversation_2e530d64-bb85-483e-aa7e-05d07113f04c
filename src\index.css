
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium FAQ Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Custom utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.7));
}

@layer base {
  :root {
    /* Enhanced Professional Medical Theme - WCAG AA Compliant */
    --background: 0 0% 100%;
    --foreground: 215 25% 12%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 12%;

    --primary: 210 85% 40%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 30% 96%;
    --secondary-foreground: 215 25% 18%;

    --muted: 210 25% 95%;
    --muted-foreground: 215 20% 32%;

    --accent: 210 25% 96%;
    --accent-foreground: 215 25% 18%;

    --destructive: 0 65% 45%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 15% 85%;
    --input: 210 15% 85%;
    --ring: 210 85% 40%;

    --radius: 0.5rem;

    /* Enhanced Professional Medical Color Palette */
    --medical-blue: 210 85% 40%;
    --medical-blue-foreground: 0 0% 100%;
    --medical-blue-light: 210 85% 97%;
    --medical-blue-dark: 210 85% 30%;

    --success: 142 65% 32%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 65% 96%;

    --warning: 38 85% 48%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 38 85% 96%;

    --error: 0 70% 47%;
    --error-foreground: 0 0% 100%;
    --error-light: 0 70% 96%;

    --info: 199 80% 40%;
    --info-foreground: 0 0% 100%;
    --info-light: 199 80% 96%;

    /* Professional Trust Colors */
    --trust-navy: 215 85% 22%;
    --trust-navy-light: 215 85% 95%;
    --trust-teal: 180 65% 32%;
    --trust-teal-light: 180 65% 95%;
  }

  .dark {
    /* Enhanced Professional Dark Theme - Improved Medical Readability */
    --background: 220 20% 8%;
    --foreground: 220 10% 98%;

    --card: 220 15% 12%;
    --card-foreground: 220 10% 98%;

    --popover: 220 15% 12%;
    --popover-foreground: 220 10% 98%;

    --primary: 210 80% 70%;
    --primary-foreground: 220 20% 8%;

    --secondary: 220 12% 18%;
    --secondary-foreground: 220 10% 92%;

    --muted: 220 12% 18%;
    --muted-foreground: 220 10% 78%;

    --accent: 220 12% 18%;
    --accent-foreground: 220 10% 92%;

    --destructive: 0 60% 65%;
    --destructive-foreground: 220 10% 98%;

    --border: 220 12% 25%;
    --input: 220 12% 25%;
    --ring: 210 80% 70%;

    /* Enhanced Professional Dark Medical Colors - Better Contrast */
    --medical-blue: 210 80% 72%;
    --medical-blue-foreground: 220 20% 8%;
    --medical-blue-light: 210 80% 15%;
    --medical-blue-dark: 210 80% 78%;

    --success: 142 60% 68%;
    --success-foreground: 220 20% 8%;
    --success-light: 142 60% 15%;

    --warning: 38 80% 70%;
    --warning-foreground: 220 20% 8%;
    --warning-light: 38 80% 15%;

    --error: 0 65% 75%;
    --error-foreground: 220 20% 8%;
    --error-light: 0 65% 15%;

    --info: 199 75% 70%;
    --info-foreground: 220 20% 8%;
    --info-light: 199 75% 15%;

    /* Professional Dark Trust Colors - Enhanced Visibility */
    --trust-navy: 215 80% 55%;
    --trust-navy-light: 215 80% 15%;
    --trust-teal: 180 60% 65%;
    --trust-teal-light: 180 60% 15%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html, body {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    font-weight: 400;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
    color: hsl(var(--foreground));
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight;
    margin-bottom: 1.5rem;
    color: hsl(var(--foreground));
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold leading-tight tracking-tight;
    margin-bottom: 1.25rem;
    color: hsl(var(--foreground));
  }

  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold leading-snug tracking-tight;
    margin-bottom: 1rem;
    color: hsl(var(--foreground));
  }

  h4 {
    @apply text-xl md:text-2xl lg:text-3xl font-medium leading-snug;
    margin-bottom: 0.875rem;
    color: hsl(var(--foreground));
  }

  h5 {
    @apply text-lg md:text-xl lg:text-2xl font-medium leading-normal;
    margin-bottom: 0.75rem;
    color: hsl(var(--foreground));
  }

  h6 {
    @apply text-base md:text-lg lg:text-xl font-medium leading-normal;
    margin-bottom: 0.625rem;
    color: hsl(var(--foreground));
  }

  p {
    @apply text-foreground/90 leading-relaxed text-base md:text-lg;
    margin-bottom: 1rem;
    line-height: 1.7;
  }

  .lead {
    @apply text-lg md:text-xl text-foreground/95 leading-relaxed font-normal;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  strong, b {
    @apply font-semibold text-foreground;
  }

  em, i {
    @apply italic text-foreground/95;
  }

  /* Enhanced Link Styling for Dark Theme */
  a {
    @apply transition-colors duration-200;
  }

  /* Broken CSS rules removed */

  /* Enhanced text hierarchy classes */
  .text-hero {
    @apply text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 2rem;
  }

  .text-display {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1.5rem;
  }

  .text-headline {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold leading-tight tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1.25rem;
  }

  .text-title {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold leading-snug tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1rem;
  }

  .text-subtitle {
    @apply text-xl md:text-2xl lg:text-3xl font-medium leading-snug;
    color: hsl(var(--foreground));
    margin-bottom: 0.875rem;
  }

  /* Medical Content Typography */
  .medical-heading {
    @apply text-2xl md:text-3xl font-bold leading-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1.5rem;
    border-bottom: 2px solid hsl(var(--primary) / 0.3);
    padding-bottom: 0.5rem;
  }

  .medical-subheading {
    @apply text-lg md:text-xl font-semibold leading-snug;
    color: hsl(var(--foreground));
    margin-bottom: 1rem;
    margin-top: 2rem;
  }

  .medical-body {
    @apply text-base leading-relaxed;
    color: hsl(var(--foreground) / 0.9);
    margin-bottom: 1.25rem;
    line-height: 1.7;
  }

  .medical-caption {
    @apply text-sm leading-normal;
    color: hsl(var(--muted-foreground));
    margin-top: 0.5rem;
    font-style: italic;
  }

  /* Remove any browser default progress bars or loading indicators */
  ::-webkit-progress-bar,
  ::-webkit-progress-value,
  progress {
    display: none !important;
  }
}

@layer components {
  /* ========================================
   * UNIFIED BUTTON SYSTEM
   * ========================================
   * ALL buttons use the buttonVariants system from /lib/button-variants.ts
   * NO additional CSS overrides should be added here
   * This ensures consistent styling across the entire application
   * ======================================== */

  .container {
    @apply px-4 md:px-6 lg:px-8 mx-auto;
  }

  /* UNIFIED BUTTON SYSTEM - All buttons use buttonVariants */

  .dark .medical-card-interactive:focus-within {
    outline-color: hsl(var(--primary) / 0.7);
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
  }

  .section {
    @apply py-12 md:py-16 lg:py-24;
  }

  /* Enhanced Glass Card with Better Contrast */
  .glass-card {
    @apply bg-card/95 backdrop-blur-lg border border-border/50 rounded-xl shadow-lg;
  }

  /* Enhanced Professional Card Styling with Better Spacing */
  .medical-card {
    @apply bg-card border border-border/40 rounded-xl shadow-sm hover:shadow-md transition-all duration-300;
    padding: 2.5rem;
    backdrop-filter: blur(8px);
  }

  .medical-card-content {
    @apply text-card-foreground space-y-6;
  }

  .medical-card-inner {
    @apply bg-card/80 border border-border/20 rounded-lg;
    padding: 2rem;
    backdrop-filter: blur(4px);
  }

  /* Enhanced Card Variants */
  .medical-card-elevated {
    @apply medical-card shadow-md hover:shadow-lg border-border/50;
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .medical-card-elevated:hover {
    transform: translateY(-2px);
  }

  .medical-card-interactive {
    @apply medical-card hover:border-primary/40 cursor-pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .medical-card-interactive:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .medical-card-feature {
    @apply medical-card border-primary/15 hover:bg-primary/10;
    background-color: hsl(var(--primary) / 0.03);
    position: relative;
    overflow: hidden;
  }

  .medical-card-feature:hover {
    background-color: hsl(var(--primary) / 0.08);
  }

  .medical-card-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.7));
  }

  /* Professional Section Backgrounds - Enhanced */
  .section-background {
    @apply bg-background;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-muted {
    @apply bg-muted/20;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-alt {
    @apply bg-accent/20;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-card {
    @apply bg-card;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-primary {
    @apply bg-primary/5;
    border-bottom: 1px solid hsl(var(--primary) / 0.1);
  }

  /* Enhanced Professional Section Spacing */
  .section-spacing {
    @apply py-16 md:py-20 lg:py-24;
  }

  .section-spacing-sm {
    @apply py-12 md:py-16 lg:py-20;
  }

  .section-spacing-lg {
    @apply py-20 md:py-24 lg:py-32;
  }

  .section-spacing-xl {
    @apply py-24 md:py-32 lg:py-40;
  }

  /* Content spacing within sections */
  .section-content-spacing {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .section-header-spacing {
    @apply mb-12 md:mb-16 lg:mb-20;
  }

  /* Enhanced Medical Content Layout with Better Spacing */
  .medical-content-layout {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .medical-section-divider {
    @apply border-t border-border/30 my-12 md:my-16 lg:my-20;
  }

  .medical-content-grid {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12;
  }

  .medical-info-panel {
    @apply bg-primary/5 border border-primary/20 rounded-lg p-6;
    backdrop-filter: blur(8px);
  }

  .dark .medical-info-panel {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
    border-color: hsl(var(--primary) / 0.3);
    box-shadow: 0 4px 16px hsl(var(--primary) / 0.1);
  }

  /* Enhanced Dark Mode Medical Cards */
  .dark .medical-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
    border-color: hsl(var(--border) / 0.5);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  }

  .dark .medical-card:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border-color: hsl(var(--border) / 0.7);
  }

  /* Content Width Management */
  .content-width-narrow {
    @apply max-w-3xl mx-auto;
  }

  .content-width-standard {
    @apply max-w-4xl mx-auto;
  }

  .content-width-wide {
    @apply max-w-6xl mx-auto;
  }

  /* Container Standardization */
  .section-container {
    @apply container mx-auto px-4 md:px-6 lg:px-8;
  }

  .section-container-narrow {
    @apply max-w-4xl mx-auto px-4 md:px-6 lg:px-8;
  }

  .section-container-wide {
    @apply max-w-7xl mx-auto px-4 md:px-6 lg:px-8;
  }

  /* Enhanced Mobile-optimized components */
  .mobile-container {
    @apply px-4 sm:px-6 md:px-8 lg:px-12 mx-auto max-w-7xl;
  }

  .mobile-section {
    @apply py-12 sm:py-16 md:py-20 lg:py-24;
  }

  .mobile-section-sm {
    @apply py-8 sm:py-12 md:py-16 lg:py-20;
  }

  .mobile-section-lg {
    @apply py-16 sm:py-20 md:py-24 lg:py-32;
  }

  /* Mobile Spacing System */
  .mobile-xs { @apply text-xs; }
  .mobile-sm { @apply text-sm; }
  .mobile-base { @apply text-base; }
  .mobile-lg { @apply text-lg; }
  .mobile-xl { @apply text-xl; }
  .mobile-2xl { @apply text-2xl; }
  .mobile-3xl { @apply text-3xl; }
  .mobile-4xl { @apply text-4xl; }

  /* Mobile Margin/Padding Spacing */
  .mobile-p-xs { @apply p-2; }
  .mobile-p-sm { @apply p-3; }
  .mobile-p-md { @apply p-4; }
  .mobile-p-lg { @apply p-6; }
  .mobile-p-xl { @apply p-8; }

  .mobile-m-xs { @apply m-2; }
  .mobile-m-sm { @apply m-3; }
  .mobile-m-md { @apply m-4; }
  .mobile-m-lg { @apply m-6; }
  .mobile-m-xl { @apply m-8; }

  /* Mobile Gap Spacing */
  .mobile-gap-xs { @apply gap-2; }
  .mobile-gap-sm { @apply gap-3; }
  .mobile-gap-md { @apply gap-4; }
  .mobile-gap-lg { @apply gap-6; }
  .mobile-gap-xl { @apply gap-8; }

  /* Mobile Margin Directional */
  .mobile-mt-xs { @apply mt-2; }
  .mobile-mt-sm { @apply mt-3; }
  .mobile-mt-md { @apply mt-4; }
  .mobile-mt-lg { @apply mt-6; }
  .mobile-mt-xl { @apply mt-8; }

  .mobile-mb-xs { @apply mb-2; }
  .mobile-mb-sm { @apply mb-3; }
  .mobile-mb-md { @apply mb-4; }
  .mobile-mb-lg { @apply mb-6; }
  .mobile-mb-xl { @apply mb-8; }

  .mobile-ml-xs { @apply ml-2; }
  .mobile-ml-sm { @apply ml-3; }
  .mobile-ml-md { @apply ml-4; }
  .mobile-ml-lg { @apply ml-6; }
  .mobile-ml-xl { @apply ml-8; }

  .mobile-mr-xs { @apply mr-2; }
  .mobile-mr-sm { @apply mr-3; }
  .mobile-mr-md { @apply mr-4; }
  .mobile-mr-lg { @apply mr-6; }
  .mobile-mr-xl { @apply mr-8; }

  /* Mobile Padding Directional */
  .mobile-pt-xs { @apply pt-2; }
  .mobile-pt-sm { @apply pt-3; }
  .mobile-pt-md { @apply pt-4; }
  .mobile-pt-lg { @apply pt-6; }
  .mobile-pt-xl { @apply pt-8; }

  .mobile-pb-xs { @apply pb-2; }
  .mobile-pb-sm { @apply pb-3; }
  .mobile-pb-md { @apply pb-4; }
  .mobile-pb-lg { @apply pb-6; }
  .mobile-pb-xl { @apply pb-8; }

  .mobile-pl-xs { @apply pl-2; }
  .mobile-pl-sm { @apply pl-3; }
  .mobile-pl-md { @apply pl-4; }
  .mobile-pl-lg { @apply pl-6; }
  .mobile-pl-xl { @apply pl-8; }

  .mobile-pr-xs { @apply pr-2; }
  .mobile-pr-sm { @apply pr-3; }
  .mobile-pr-md { @apply pr-4; }
  .mobile-pr-lg { @apply pr-6; }
  .mobile-pr-xl { @apply pr-8; }

  /* Mobile Space Utilities */
  .mobile-space-y-xs { @apply space-y-2; }
  .mobile-space-y-sm { @apply space-y-3; }
  .mobile-space-y-md { @apply space-y-4; }
  .mobile-space-y-lg { @apply space-y-6; }
  .mobile-space-y-xl { @apply space-y-8; }

  .mobile-space-x-xs { @apply space-x-2; }
  .mobile-space-x-sm { @apply space-x-3; }
  .mobile-space-x-md { @apply space-x-4; }
  .mobile-space-x-lg { @apply space-x-6; }
  .mobile-space-x-xl { @apply space-x-8; }

  /* Professional Badge Styles with Enhanced Contrast */
  .badge-emergency {
    @apply bg-muted-light text-foreground border border-border/70 font-semibold shadow-sm;
  }

  .badge-info {
    @apply bg-info-light text-info border border-info/30 font-semibold shadow-sm;
  }

  .badge-routine {
    @apply bg-success-light text-success border border-success/30 font-semibold shadow-sm;
  }

  .badge-medical {
    @apply bg-medical-blue-light text-medical-blue border border-medical-blue/30 font-semibold shadow-sm;
  }

  /* Color Utility Classes */
  .bg-info-light {
    background-color: hsl(var(--info-light));
  }

  .bg-success-light {
    background-color: hsl(var(--success-light));
  }

  .bg-muted-light {
    background-color: hsl(var(--muted));
  }

  .bg-medical-blue-light {
    background-color: hsl(var(--medical-blue-light));
  }

  .text-info {
    color: hsl(var(--info));
  }

  .text-success {
    color: hsl(var(--success));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  .text-medical-blue {
    color: hsl(var(--medical-blue));
  }

  .text-medical-blue-foreground {
    color: hsl(var(--medical-blue-foreground));
  }

  .border-info\/30 {
    border-color: hsl(var(--info) / 0.3);
  }

  .border-success\/30 {
    border-color: hsl(var(--success) / 0.3);
  }

  .border-border\/30 {
    border-color: hsl(var(--border) / 0.3);
  }

  .border-medical-blue\/30 {
    border-color: hsl(var(--medical-blue) / 0.3);
  }

  /* Additional Theme Color Classes */
  .bg-info {
    background-color: hsl(var(--info));
  }

  .bg-success {
    background-color: hsl(var(--success));
  }

  .bg-muted {
    background-color: hsl(var(--muted));
  }

  .bg-medical-blue {
    background-color: hsl(var(--medical-blue));
  }

  .border-info {
    border-color: hsl(var(--info));
  }

  .border-success {
    border-color: hsl(var(--success));
  }

  .border-border {
    border-color: hsl(var(--border));
  }

  .border-medical-blue {
    border-color: hsl(var(--medical-blue));
  }

  .border-background {
    border-color: hsl(var(--background));
  }

  .border-foreground {
    border-color: hsl(var(--foreground));
  }

  /* Emergency styling */
  .border-emergency {
    @apply border-border;
  }

  .border-emergency\/30 {
    @apply border-border/70;
  }

  .bg-emergency-light {
    @apply bg-muted-light;
  }

  .text-emergency {
    @apply text-foreground;
  }

  /* Enhanced Text Readability with Clear Visual Hierarchy */
  .text-enhanced {
    @apply text-foreground font-medium leading-relaxed;
  }

  .text-enhanced-light {
    @apply text-foreground/75 font-normal leading-relaxed;
  }

  .text-enhanced-caption {
    @apply text-primary font-semibold leading-normal text-sm uppercase tracking-wider;
  }

  .text-enhanced-link {
    @apply text-primary font-semibold hover:text-primary/80 transition-colors duration-200;
  }

  /* Professional Text Alignment Classes */
  .text-center-aligned {
    @apply text-center items-center justify-center;
  }

  .text-left-aligned {
    @apply text-left items-start justify-start;
  }

  .text-right-aligned {
    @apply text-right items-end justify-end;
  }

  /* Consistent Icon Alignment */
  .icon-text-aligned {
    @apply flex items-center gap-2;
  }

  .icon-text-aligned-vertical {
    @apply flex flex-col items-center gap-2;
  }

  /* Professional Spacing Patterns */
  .content-spacing-sm {
    @apply space-y-4 md:space-y-6;
  }

  .content-spacing-md {
    @apply space-y-6 md:space-y-8;
  }

  .content-spacing-lg {
    @apply space-y-8 md:space-y-12;
  }

  /* Additional Enhanced Text Classes */
  .text-enhanced-small {
    @apply text-foreground/80 text-sm font-medium leading-relaxed;
  }

  .text-enhanced-large {
    @apply text-foreground text-lg font-medium leading-relaxed;
  }

  .link-enhanced-subtle {
    @apply text-muted-foreground hover:text-foreground transition-colors duration-200;
  }

  /* Professional Card Enhancements */
  .card-enhanced {
    @apply bg-card border border-border/50 rounded-lg shadow-sm hover:shadow-md transition-all duration-300;
  }

  .card-enhanced-interactive {
    @apply bg-card border border-border/50 rounded-lg shadow-sm hover:shadow-lg hover:border-border transition-all duration-300 cursor-pointer;
  }

  /* Technology Section Specific Styling */
  .technology-card {
    @apply medical-card p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105;
    @apply border border-border/30 hover:border-primary/20;
  }

  .technology-icon-container {
    @apply rounded-2xl flex items-center justify-center shadow-lg backdrop-blur-sm;
    @apply transition-all duration-300 hover:shadow-xl hover:scale-110;
  }

  /* Professional Animation Classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Professional Button Enhancements */
  /* Button enhancements are handled by buttonVariants */

  /* Professional Section Styling */
  .section-enhanced {
    @apply py-16 md:py-20 lg:py-24 bg-background border-y border-border/20;
  }

  .section-enhanced-alt {
    @apply py-16 md:py-20 lg:py-24 bg-muted/30 border-y border-border/20;
  }

  .section-enhanced-primary {
    @apply py-16 md:py-20 lg:py-24 bg-primary/5 border-y border-primary/20;
  }

  /* Enhanced Visual Hierarchy */
  .visual-hierarchy-section {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .visual-hierarchy-content {
    @apply space-y-6 md:space-y-8;
  }

  /* Enhanced Grid Layouts */
  .grid-technology {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8;
  }

  .grid-features {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8;
  }

  .grid-services {
    @apply grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8;
  }

  /* Professional Spacing Utilities */
  .spacing-technology {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .spacing-content {
    @apply space-y-6 md:space-y-8 lg:space-y-10;
  }

  .spacing-section {
    @apply space-y-12 md:space-y-16 lg:space-y-20;
  }

  /* Enhanced Color Utilities */
  .enhanced-border {
    @apply border-border/50;
  }

  .enhanced-hover {
    @apply hover:bg-accent/50;
  }

  .enhanced-accent {
    @apply text-primary;
  }

  .bg-enhanced-accent {
    @apply bg-primary/10;
  }

  /* Visual Hierarchy Spacing */
  .section-spacing {
    @apply py-16 md:py-20 lg:py-24;
  }

  .section-spacing-sm {
    @apply py-12 md:py-16 lg:py-20;
  }

  .content-spacing {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  /* Enhanced Text Content Spacing */
  .text-content-spacing {
    @apply space-y-6 md:space-y-8;
  }

  .text-content-spacing p {
    @apply mb-6 leading-relaxed;
  }

  .text-content-spacing h2 {
    @apply mt-12 mb-6;
  }

  .text-content-spacing h3 {
    @apply mt-8 mb-4;
  }

  /* Enhanced Card Hierarchy */
  .card-primary {
    @apply medical-card border-primary/20 shadow-lg hover:shadow-xl;
  }

  /* Enhanced Medical Card System */
  .medical-content-card {
    @apply bg-card border border-border/40 rounded-xl shadow-sm hover:shadow-md transition-all duration-300;
    padding: 2.5rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(8px);
  }

  .medical-content-card-sm {
    @apply bg-card border border-border/40 rounded-lg shadow-sm hover:shadow-md transition-all duration-300;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .medical-content-card-lg {
    @apply bg-card border border-border/40 rounded-xl shadow-md hover:shadow-lg transition-all duration-300;
    padding: 3rem;
    margin-bottom: 2.5rem;
    backdrop-filter: blur(12px);
  }

  .medical-content-card h1 {
    @apply medical-heading mb-8;
  }

  .medical-content-card h2 {
    @apply medical-subheading mb-6;
  }

  .medical-content-card h3 {
    @apply text-foreground font-semibold text-lg md:text-xl mb-4;
  }

  .medical-content-card h4 {
    @apply text-foreground font-medium text-base md:text-lg mb-3;
  }

  .medical-content-card p {
    @apply medical-body;
  }

  .medical-content-card ul,
  .medical-content-card ol {
    @apply mb-6 space-y-3 pl-6;
  }

  .medical-content-card li {
    @apply text-muted-foreground leading-relaxed;
  }

  /* All

  .medical-subheading {
    @apply text-foreground font-semibold text-xl md:text-2xl lg:text-3xl leading-tight;
    letter-spacing: -0.015em;
    margin-bottom: 1rem;
  }

  .medical-body {
    @apply text-muted-foreground text-base md:text-lg leading-relaxed;
    line-height: 1.7;
    margin-bottom: 1.5rem;
  }

  .medical-body-sm {
    @apply text-muted-foreground text-sm md:text-base leading-relaxed;
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .medical-caption {
    @apply text-muted-foreground text-sm italic leading-relaxed;
    margin-top: 0.5rem;
  }

  .medical-label {
    @apply text-foreground font-medium text-sm uppercase tracking-wide;
    letter-spacing: 0.05em;
  }

  .medical-lead {
    @apply text-foreground text-lg md:text-xl leading-relaxed font-medium;
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  /* Enhanced Text Utilities */
  .text-enhanced-heading {
    @apply text-foreground font-bold text-2xl md:text-3xl lg:text-4xl leading-tight;
    letter-spacing: -0.025em;
  }

  .text-enhanced-subheading {
    @apply text-foreground font-semibold text-lg md:text-xl lg:text-2xl leading-tight;
    letter-spacing: -0.015em;
  }

  .text-enhanced-body {
    @apply text-muted-foreground text-base md:text-lg leading-relaxed;
    line-height: 1.7;
  }

  .text-enhanced-body-sm {
    @apply text-muted-foreground text-sm md:text-base leading-relaxed;
    line-height: 1.6;
  }

  .text-enhanced-muted {
    @apply text-muted-foreground text-sm leading-relaxed;
  }

  .text-enhanced-label {
    @apply text-foreground font-medium text-sm uppercase tracking-wide;
    letter-spacing: 0.05em;
  }

  /* Location Page Typography */
  .location-title {
    @apply text-foreground font-bold text-3xl md:text-4xl lg:text-5xl leading-tight;
    letter-spacing: -0.025em;
  }

  .location-subtitle {
    @apply text-primary font-semibold text-xl md:text-2xl lg:text-3xl leading-tight;
    letter-spacing: -0.015em;
  }

  .location-section-title {
    @apply text-foreground font-bold text-2xl md:text-3xl leading-tight mb-4;
    letter-spacing: -0.02em;
  }

  .location-card-title {
    @apply text-primary font-semibold text-lg md:text-xl leading-tight;
    letter-spacing: -0.01em;
  }

  .location-body-text {
    @apply text-muted-foreground text-base leading-relaxed;
    line-height: 1.7;
  }

  .location-small-text {
    @apply text-muted-foreground text-sm leading-relaxed;
    line-height: 1.6;
  }

  /* Professional List Styling */
  .medical-list {
    @apply space-y-3 pl-6;
  }

  .medical-list li {
    @apply text-muted-foreground leading-relaxed relative;
  }

  .medical-list li::before {
    content: "•";
    @apply text-primary font-bold absolute -left-4;
  }

  .medical-list-ordered {
    @apply space-y-3 pl-6 counter-reset: medical-counter;
  }

  .medical-list-ordered li {
    @apply text-muted-foreground leading-relaxed relative counter-increment: medical-counter;
  }

  .medical-list-ordered li::before {
    content: counter(medical-counter) ".";
    @apply text-primary font-bold absolute -left-6 w-4 text-right;
  }

  /* Enhanced Card System Variants */
  .card-elevated {
    @apply bg-card border border-border/40 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
    backdrop-filter: blur(12px);
  }

  .card-interactive {
    @apply bg-card border border-border/40 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer;
    backdrop-filter: blur(8px);
  }

  .card-interactive:hover {
    transform: translateY(-2px);
    border-color: hsl(var(--primary) / 0.3);
  }

  .card-feature {
    @apply bg-card border border-border/40 rounded-xl shadow-md hover:shadow-lg transition-all duration-300;
    padding: 2rem;
    backdrop-filter: blur(10px);
  }

  .card-testimonial {
    @apply bg-card border border-border/30 rounded-xl shadow-sm hover:shadow-md transition-all duration-300;
    padding: 2rem;
    backdrop-filter: blur(6px);
  }

  .card-service {
    @apply bg-card border border-border/40 rounded-xl shadow-md hover:shadow-lg transition-all duration-300;
    padding: 2.5rem;
    backdrop-filter: blur(10px);
  }

  .card-service:hover {
    transform: translateY(-3px);
    border-color: hsl(var(--primary) / 0.4);
  }

  .card-highlight {
    @apply bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 rounded-xl shadow-md hover:shadow-lg transition-all duration-300;
    padding: 2rem;
    backdrop-filter: blur(8px);
  }

  .card-subtle {
    @apply bg-muted/30 border border-border/20 rounded-lg shadow-sm hover:shadow-md transition-all duration-300;
    padding: 1.5rem;
    backdrop-filter: blur(4px);
  }

  /* Comprehensive Responsive Design System */

  /* Mobile-First Responsive Containers */
  .responsive-container {
    @apply w-full mx-auto px-4 sm:px-6 lg:px-8;
    max-width: 1280px;
  }

  .responsive-container-sm {
    @apply w-full mx-auto px-4 sm:px-6;
    max-width: 640px;
  }

  .responsive-container-lg {
    @apply w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-12;
    max-width: 1536px;
  }

  /* Responsive Typography */
  .responsive-heading {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold leading-tight;
    letter-spacing: -0.025em;
  }

  .responsive-subheading {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold leading-tight;
    letter-spacing: -0.015em;
  }

  .responsive-body {
    @apply text-sm sm:text-base md:text-lg leading-relaxed;
    line-height: 1.7;
  }

  /* Responsive Spacing */
  .responsive-padding {
    @apply p-4 sm:p-6 md:p-8 lg:p-12;
  }

  .responsive-margin {
    @apply m-4 sm:m-6 md:m-8 lg:m-12;
  }

  .responsive-gap {
    @apply gap-4 sm:gap-6 md:gap-8 lg:gap-12;
  }

  /* Responsive Grid Systems */
  .responsive-grid-2 {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8;
  }

  .responsive-grid-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8;
  }

  .responsive-grid-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 md:gap-8;
  }

  /* Mobile Touch Targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  .touch-target-lg {
    @apply min-h-[48px] min-w-[48px] touch-manipulation;
  }

  /* Responsive Buttons */
  .responsive-

  /* Mobile-Optimized Cards */
  .mobile-card-optimized {
    @apply p-4 sm:p-6 md:p-8 rounded-lg sm:rounded-xl;
  }

  /* Responsive Images */
  .responsive-image {
    @apply w-full h-auto object-cover rounded-lg;
  }

  .responsive-image-hero {
    @apply w-full h-48 sm:h-64 md:h-80 lg:h-96 object-cover rounded-lg;
  }

  /* WCAG AA Accessibility Compliance System */

  /* Focus States - Enhanced for Accessibility */
  .focus-enhanced {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  .focus-enhanced-

  .focus-enhanced-input {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .medical-card {
      border-width: 2px;
      border-color: hsl(var(--border));
    }
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .medical-card-interactive:hover {
      transform: none;
    }
  }

  /* Screen Reader Only Content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Skip Links */
  .skip-link {
    @apply absolute -top-40 left-6 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium;
    transition: top 0.3s;
  }

  .skip-link:focus {
    @apply top-6;
  }

  /* Enhanced Color Contrast */
  .text-high-contrast {
    @apply text-foreground;
    color: hsl(var(--foreground));
  }

  .bg-high-contrast {
    @apply bg-background;
    background-color: hsl(var(--background));
  }

  /* Accessible Form Elements */
  .form-field-accessible {
    @apply space-y-2;
  }

  .form-label-accessible {
    @apply text-sm font-semibold text-foreground block mb-2;
  }

  .form-input-accessible {
    @apply w-full px-4 py-3 border-2 border-input rounded-lg bg-background text-foreground focus-enhanced-input;
    min-height: 48px;
  }

  .form-error-accessible {
    @apply text-sm text-foreground font-medium mt-2 flex items-center;
  }

  .form-description-accessible {
    @apply text-sm text-muted-foreground mt-2 leading-relaxed;
  }

  /* Accessible Interactive States */
  .interactive-accessible {
    @apply cursor-pointer focus-enhanced transition-all duration-200;
  }

  .interactive-accessible:hover {
    @apply bg-muted/50;
  }

  .interactive-accessible:active {
    @apply bg-muted/70;
  }

  /* ARIA Live Regions */
  .live-region {
    @apply sr-only;
  }

  /* Accessible Color Combinations */
  .text-accessible-primary {
    color: hsl(var(--primary));
    /* Ensures 4.5:1 contrast ratio */
  }

  .text-accessible-secondary {
    color: hsl(var(--muted-foreground));
    /* Ensures 4.5:1 contrast ratio */
  }

  .bg-accessible-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .bg-accessible-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
  }

  /* Cross-Browser Compatibility Enhancements */

  /* Webkit/Safari Specific */
  @supports (-webkit-backdrop-filter: blur(10px)) {
    .medical-card,
    .medical-content-card,
    .card-elevated,
    .card-interactive {
      -webkit-backdrop-filter: blur(8px);
    }
  }

  /* Firefox Specific */
  @-moz-document url-prefix() {
    .medical-card,
    .medical-content-card {
      background-color: hsl(var(--card) / 0.95);
    }
  }

  /* Edge/IE Fallbacks */
  @supports not (backdrop-filter: blur(8px)) {
    .medical-card,
    .medical-content-card,
    .card-elevated,
    .card-interactive {
      background-color: hsl(var(--card) / 0.98);
    }
  }

  /* Touch Device Optimizations */
  @media (hover: none) and (pointer: coarse) {
    .medical-card-interactive:hover {
      transform: none;
    }

    .interactive-accessible:hover {
      background-color: transparent;
    }
  }

  /* High DPI Display Support */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .medical-card,
    .medical-content-card {
      border-width: 0.5px;
    }
  }

  /* Print Styles */
  @media print {
    .medical-card,
    .medical-content-card {
      background: white !important;
      border: 1px solid #000 !important;
      box-shadow: none !important;
      backdrop-filter: none !important;
    }

    .text-muted-foreground {
      color: #666 !important;
    }
  }

  /* Browser-Specific Button Resets */

  /* Input Field Consistency */
  input,
  textarea,
  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-clip: padding-box;
  }

  /* Scrollbar Styling for Webkit */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.5);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.7);
  }

  /* Firefox Scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.5) hsl(var(--muted) / 0.3);
  }

  /* Performance Optimizations */

  /* GPU Acceleration for Smooth Animations */
  .medical-card-interactive,

  /* Optimize Backdrop Filters */
  .medical-card,
  .medical-content-card,
  .card-elevated {
    contain: layout style paint;
  }

  /* Efficient Transitions */
  .transition-optimized {
    transition-property: transform, opacity, box-shadow, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Reduce Paint Operations */
  .medical-card,
  .medical-content-card {
    isolation: isolate;
  }

  /* Optimize Font Loading */
  .medical-heading,
  .medical-subheading,
  .medical-body {
    font-display: swap;
  }

  /* Critical CSS Optimizations */
  .above-fold {
    contain: layout;
  }

  /* Lazy Loading Optimizations */
  .lazy-load {
    content-visibility: auto;
    contain-intrinsic-size: 200px;
  }

  /* Reduce Reflow/Repaint */
  .layout-stable {
    contain: layout style;
  }

  /* Memory Efficient Shadows */
  .shadow-optimized {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .shadow-optimized-md {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .shadow-optimized-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  /* Efficient Grid Layouts */
  .grid-optimized {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  /* Optimized Flexbox */
  .flex-optimized {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }

  /* Reduce Layout Thrashing */
  .no-layout-shift {
    aspect-ratio: attr(width) / attr(height);
  }

  /* Efficient Color Calculations */
  .color-optimized {
    color: hsl(var(--foreground));
  }

  .color-optimized-muted {
    color: hsl(var(--muted-foreground));
  }

  /* Performance Monitoring */
  @media (prefers-reduced-data: reduce) {
    .medical-card,
    .medical-content-card {
      backdrop-filter: none;
      background-color: hsl(var(--card));
    }

    .shadow-optimized,
    .shadow-optimized-md,
    .shadow-optimized-lg {
      box-shadow: none;
      border: 1px solid hsl(var(--border));
    }
  }

  /* Enhanced Interactive Elements */
  .medical-card-interactive {
    @apply hover:border-primary/30 hover:shadow-lg transition-all duration-300;
  }

  .medical-card-interactive:hover {
    transform: translateY(-2px);
  }

  /* Comprehensive Spacing System */
  .spacing-xs { @apply space-y-2; }
  .spacing-sm { @apply space-y-4; }
  .spacing-md { @apply space-y-6 md:space-y-8; }
  .spacing-lg { @apply space-y-8 md:space-y-12 lg:space-y-16; }
  .spacing-xl { @apply space-y-12 md:space-y-16 lg:space-y-20; }
  .spacing-2xl { @apply space-y-16 md:space-y-20 lg:space-y-24; }

  /* Section Spacing */
  .section-padding-sm { @apply py-8 md:py-12; }
  .section-padding-md { @apply py-12 md:py-16 lg:py-20; }
  .section-padding-lg { @apply py-16 md:py-20 lg:py-24; }
  .section-padding-xl { @apply py-20 md:py-24 lg:py-32; }

  /* Container Spacing */
  .container-padding { @apply px-4 md:px-6 lg:px-8; }
  .container-spacing { @apply max-w-7xl mx-auto px-4 md:px-6 lg:px-8; }

  /* Content Width Variants */
  .content-width-narrow { @apply max-w-2xl mx-auto; }
  .content-width-standard { @apply max-w-4xl mx-auto; }
  .content-width-wide { @apply max-w-6xl mx-auto; }
  .content-width-full { @apply max-w-7xl mx-auto; }

  /* Grid Spacing Variants */
  .grid-spacing-sm { @apply gap-4 md:gap-6; }
  .grid-spacing-md { @apply gap-6 md:gap-8 lg:gap-10; }
  .grid-spacing-lg { @apply gap-8 md:gap-12 lg:gap-16; }
  .grid-spacing-xl { @apply gap-12 md:gap-16 lg:gap-20; }

  /* Margin Utilities */
  .margin-section { @apply my-16 md:my-20 lg:my-24; }
  .margin-component { @apply my-8 md:my-12 lg:my-16; }
  .margin-element { @apply my-4 md:my-6 lg:my-8; }

  /* Padding Utilities */
  .padding-section { @apply p-8 md:p-12 lg:p-16; }
  .padding-component { @apply p-6 md:p-8 lg:p-10; }
  .padding-element { @apply p-4 md:p-6 lg:p-8; }

  /* Professional Layout Patterns */
  .layout-two-column {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16;
  }

  .layout-three-column {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-10;
  }

  .layout-four-column {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8;
  }

  .layout-sidebar {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-8 md:gap-12 lg:gap-16;
  }

  .layout-sidebar-content {
    @apply lg:col-span-2;
  }

  .layout-sidebar-aside {
    @apply lg:col-span-1;
  }

  /* Enhanced Button Spacing and Visibility */
  /* Enhanced Card Styling */

  .card-accent {
    @apply medical-card bg-accent/50 border-accent/30 shadow-sm hover:shadow-md;
  }

  /* Medical Content Cards - Enhanced for Dark Theme */
  .medical-content-card {
    @apply bg-card border border-border/40 rounded-xl shadow-lg;
    backdrop-filter: blur(12px);
    position: relative;
  }

  .dark .medical-content-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.95) 100%);
    border-color: hsl(var(--border) / 0.6);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  /* Enhanced Dark Mode Text Readability */
  .dark .text-enhanced-body {
    color: hsl(var(--foreground) / 0.92);
  }

  .dark .text-enhanced-muted {
    color: hsl(var(--muted-foreground) / 0.85);
  }

  .dark .text-enhanced-heading {
    color: hsl(var(--foreground));
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .medical-image-container {
    @apply rounded-lg overflow-hidden border border-border/30;
    background: hsl(var(--background));
  }

  .dark .medical-image-container {
    background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--card)) 100%);
    border-color: hsl(var(--border) / 0.5);
  }

  .link-enhanced-subtle {
    @apply text-foreground/80 hover:text-primary transition-colors duration-200;
  }

  /* Professional Button Styling - Enhanced for Medical Standards */

  /* Enhanced Badge Styling */
  .badge-enhanced {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-primary {
    @apply badge-enhanced bg-primary/10 text-primary border border-primary/20;
  }

  .badge-secondary {
    @apply badge-enhanced bg-secondary/10 text-secondary-foreground border border-secondary/20;
  }

  .badge-success {
    @apply badge-enhanced bg-success-light text-success border border-success/30;
  }

  .badge-warning {
    @apply badge-enhanced bg-warning-light text-warning border border-warning/30;
  }
}

  /* Enhanced Mobile Components */
  .mobile-card {
    @apply bg-card rounded-xl shadow-sm border border-border/40 p-6 transition-all duration-300;
    backdrop-filter: blur(8px);
  }

  .dark .mobile-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
    border-color: hsl(var(--border) / 0.5);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .mobile-button {
    @apply min-h-[48px] px-6 py-3 text-base font-medium rounded-lg touch-manipulation;
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
    @apply transition-all duration-200 active:scale-95;
  }

  .dark .mobile-button {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .mobile-input {
    @apply min-h-[48px] text-base touch-manipulation rounded-lg border-2 border-border/50 focus:border-primary;
    padding: 12px 16px;
  }

  .mobile-text {
    @apply text-base leading-relaxed;
    line-height: 1.6;
  }

  .mobile-heading {
    @apply text-2xl font-bold leading-tight;
    margin-bottom: 1rem;
  }

  .mobile-subheading {
    @apply text-xl font-semibold leading-snug;
    margin-bottom: 0.75rem;
  }

  /* Enhanced Touch-friendly utilities */
  .touch-target-enhanced {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center touch-manipulation;
    position: relative;
  }

  /* Mobile-optimized button styles */
  .mobile-button-primary {
    @apply min-h-[48px] px-6 py-3 text-base font-medium rounded-lg touch-manipulation;
    @apply bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95;
    @apply transition-all duration-200 active:scale-95;
    @apply w-full sm:w-auto;
  }

  .mobile-button-secondary {
    @apply min-h-[48px] px-6 py-3 text-base font-medium rounded-lg touch-manipulation;
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 active:bg-secondary/95;
    @apply border border-border transition-all duration-200 active:scale-95;
    @apply w-full sm:w-auto;
  }

  /* Enhanced Mobile Grid Systems */
  .mobile-grid-responsive {
    @apply grid grid-cols-1 gap-4 sm:gap-6 md:gap-8;
  }

  .mobile-grid-2 {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8;
  }

  .mobile-grid-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8;
  }

  .mobile-grid-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 md:gap-8;
  }

  /* Mobile-first flex utilities */
  .mobile-flex-col {
    @apply flex flex-col gap-4 sm:flex-row sm:gap-6;
  }

  .mobile-flex-center {
    @apply flex flex-col items-center gap-4 sm:flex-row sm:justify-center sm:gap-6;
  }

  /* Enhanced mobile card spacing */
  .mobile-card-spacing {
    @apply p-4 sm:p-6 md:p-8;
  }

  .mobile-section-spacing {
    @apply py-8 sm:py-12 md:py-16 lg:py-20;
  }

  .touch-feedback {
    @apply transition-all duration-150 active:scale-95 touch-manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .touch-feedback:active {
    transform: scale(0.95);
    opacity: 0.8;
  }

  .swipe-container {
    @apply overflow-x-auto scrollbar-hide touch-pan-x;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
  }

  .swipe-item {
    scroll-snap-align: start;
    flex-shrink: 0;
  }

  .wave-animation {
    animation: wave 12s linear infinite;
    animation-delay: -2s;
    transform-origin: center bottom;
  }

  .page-transition-enter {
    opacity: 0;
    transform: translateY(10px);
  }

  .page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 400ms, transform 400ms;
  }

  .page-transition-exit {
    opacity: 1;
  }

  .page-transition-exit-active {
    opacity: 0;
    transition: opacity 300ms;
  }

  /* Mobile-specific utilities */
  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .mobile-scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  .mobile-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Hide scrollbars on mobile */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Touch-specific styles */
  @media (hover: none) and (pointer: coarse) {
    .hover-only {
      display: none;
    }

    .touch-only {
      display: block;
    }

    /* Larger touch targets on touch devices */

    /* Remove hover effects on touch devices */
    .hover\:scale-105:hover {
      transform: none;
    }

    .hover\:shadow-lg:hover {
      box-shadow: none;
    }
  }

  @media (hover: hover) and (pointer: fine) {
    .touch-only {
      display: none;
    }

    .hover-only {
      display: block;
    }
  }

  /* Enhanced Mobile typography improvements */
  @media (max-width: 640px) {
    h1 {
      font-size: 2.25rem;
      line-height: 2.5rem;
      word-wrap: break-word;
      hyphens: auto;
      margin-bottom: 1.5rem;
    }

    h2 {
      font-size: 1.875rem;
      line-height: 2.25rem;
      margin-bottom: 1.25rem;
    }

    h3 {
      font-size: 1.5rem;
      line-height: 1.875rem;
      margin-bottom: 1rem;
    }

    h4 {
      font-size: 1.25rem;
      line-height: 1.625rem;
      margin-bottom: 0.875rem;
    }

    p {
      font-size: 1rem;
      line-height: 1.6rem;
      word-wrap: break-word;
      margin-bottom: 1rem;
    }

    .lead {
      font-size: 1.125rem;
      line-height: 1.75rem;
      margin-bottom: 1.5rem;
    }

    /* Enhanced mobile dark mode text */
    .dark h1, .dark h2, .dark h3, .dark h4 {
      color: hsl(var(--foreground));
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .dark p {
      color: hsl(var(--foreground) / 0.9);
    }

    .dark .lead {
      color: hsl(var(--foreground) / 0.95);
    }
  }

  /* Responsive text overflow prevention */
  .text-responsive {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Breadcrumb responsive improvements */
  .breadcrumb-responsive {
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* Accessibility Enhancements */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Enhanced screen reader support */
  .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem 1rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border-radius: 4px;
    font-weight: 600;
    z-index: 9999;
  }

  /* ARIA live region styling */
  .aria-live-region {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
  }

  .aria-live-region.polite {
    speak: normal;
  }

  .aria-live-region.assertive {
    speak: assertive;
  }

  .skip-link {
    position: absolute;
    top: -100px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
    opacity: 0;
    pointer-events: none;
  }

  .skip-link:focus {
    top: 6px;
    opacity: 1;
    pointer-events: auto;
  }

  /* Enhanced Focus management for accessibility */
  .keyboard-navigation *:focus-visible {
    outline: 3px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: 4px;
  }

  .keyboard-navigation

  /* Enhanced dark mode focus states */
  .dark .keyboard-navigation *:focus-visible {
    outline-color: hsl(var(--primary));
    box-shadow: 0 0 0 6px hsl(var(--primary) / 0.3);
  }

  /* Skip link enhancements */
  .skip-link:focus {
    top: 6px;
    opacity: 1;
    pointer-events: auto;
    z-index: 9999;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --border: 0 0% 0%;
      --input: 0 0% 0%;
      --foreground: 0 0% 0%;
      --background: 0 0% 100%;
      --primary: 210 100% 30%;
      --muted-foreground: 0 0% 20%;
    }

    .dark {
      --border: 0 0% 100%;
      --input: 0 0% 100%;
      --foreground: 0 0% 100%;
      --background: 0 0% 0%;
      --primary: 210 100% 80%;
      --muted-foreground: 0 0% 80%;
    }

    /* Enhanced contrast for interactive elements */

    .medical-card, .mobile-card {
      border-width: 2px;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .wave-animation {
      animation: none;
    }
  }

  /* Cross-browser compatibility enhancements */
  /* CSS Custom Properties fallbacks for older browsers */

  .medical-card {
    background-color: white; /* Fallback */
    background: hsl(var(--card));
    border-color: #e0e0e0; /* Fallback */
    border-color: hsl(var(--border) / 0.4);
  }

  /* Webkit-specific enhancements */
  @supports (-webkit-backdrop-filter: blur(8px)) {
    .medical-card, .mobile-card {
      -webkit-backdrop-filter: blur(8px);
    }
  }

  /* Firefox-specific enhancements */
  @-moz-document url-prefix() {
    .medical-card, .mobile-card {
      background-color: hsl(var(--card) / 0.95);
    }

    .dark .medical-card, .dark .mobile-card {
      background-color: hsl(var(--card) / 0.95);
    }
  }

  /* Defensive CSS against external tool interference */
  /* Prevent external tools from affecting our layout */
  body > div:not(#root),
  body > iframe:not([src*="mineuro.com.au"]),
  body > [class*="extension-"],
  body > [id*="extension-"],
  body > [class*="widget-"],
  body > [id*="widget-"] {
    z-index: 999999 !important;
    pointer-events: auto !important;
  }

  /* Ensure our content stays properly aligned */
  #root {
    position: relative;
    z-index: 1;
  }

  /* Prevent external styles from affecting our
  }

  /* Reduced motion support for accessibility */
  @media (prefers-reduced-motion: reduce) {
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }

  /* Performance optimizations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Cross-browser theme consistency */
  /* Ensure consistent rendering across browsers */
  * {
    box-sizing: border-box;
  }

  /* Safari-specific fixes */
  @supports (-webkit-appearance: none) {

    input, select, textarea {
      -webkit-appearance: none;
      border-radius: 0;
    }
  }

  /* Edge/IE compatibility */
  @supports (-ms-ime-align: auto) {
    .medical-card {
      background: hsl(var(--card));
      border: 1px solid hsl(var(--border));
    }

    .dark .medical-card {
      background: hsl(var(--card));
      border: 1px solid hsl(var(--border));
    }
  }

  /* Loading states */
  .loading-skeleton {
    background: linear-gradient(90deg,
      hsl(var(--muted)) 25%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
    }

    a[href]:after {
      content: " (" attr(href) ")";
    }

    .page-break {
      page-break-before: always;
    }
  }
