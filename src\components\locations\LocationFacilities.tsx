import React from 'react';

import LocationGallery from './LocationGallery';

interface Facility {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface AdditionalFacility {
  title: string;
  description: string;
  additionalInfo?: string;
}

interface GalleryImage {
  src: string;
  alt: string;
}

interface LocationFacilitiesProps {
  title: string;
  subtitle: string;
  description: string;
  facilities: Facility[];
  additionalFacilities?: AdditionalFacility[];
  gallery?: {
    title: string;
    description: string;
    images: GalleryImage[];
  };
}

const LocationFacilities: React.FC<LocationFacilitiesProps> = ({
  title,
  subtitle,
  description,
  facilities,
  additionalFacilities,
  gallery
}) => {
  return (
    <section className="bg-primary/5 section-padding-lg">
      <div className="container-spacing">
        <div className="text-center section-header-spacing">
          <h2 className="location-section-title">{title}</h2>
          <p className="location-body-text max-w-3xl mx-auto">{subtitle}</p>
        </div>

        <div className="max-w-3xl mx-auto mb-12">
          <p className="location-body-text text-center">{description}</p>
        </div>

        <div className="mobile-grid-3 mb-12">
          {facilities.map((facility, index) => (
            <div key={index} className="medical-card p-6 text-center hover:shadow-lg transition-shadow duration-300">
              <div className="flex justify-center mb-4">
                <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                  {facility.icon}
                </div>
              </div>
              <h3 className="location-card-title mb-3">{facility.title}</h3>
              <p className="location-body-text">{facility.description}</p>
            </div>
          ))}
        </div>

        {additionalFacilities && additionalFacilities.length > 0 && (
          <div className="mobile-grid-2 mb-12">
            {additionalFacilities.map((facility, index) => (
              <div key={index} className="medical-card p-6 hover:shadow-lg transition-shadow duration-300">
                <h3 className="location-card-title mb-3">{facility.title}</h3>
                <p className="location-body-text mb-4">
                  {facility.description}
                </p>
                {facility.additionalInfo && (
                  <p className="location-body-text">
                    {facility.additionalInfo}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        {gallery && (
          <LocationGallery
            title={gallery.title}
            description={gallery.description}
            images={gallery.images}
          />
        )}
      </div>
    </section>
  );
};

export default LocationFacilities;
