import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface LocationHeroProps {
  title: string;
  subtitle: string;
  introduction1: string;
  introduction2?: string;
  introduction3?: string;
  imageUrl?: string;
  ctaText?: string;
  ctaLink?: string;
}

const LocationHero: React.FC<LocationHeroProps> = ({
  title,
  subtitle,
  introduction1,
  introduction2,
  introduction3,
  imageUrl,
  ctaText = "Book an Appointment",
  ctaLink = "/appointments"
}) => {
  return (
    <section className="relative section-padding-lg bg-gradient-to-r from-primary/10 to-background/50 dark:from-primary/20 dark:to-background">
      <div className="container-spacing relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          <div className="space-y-6">
            <h1 className="location-title">{title}</h1>
            <h2 className="location-subtitle">{subtitle}</h2>
            <div className="space-y-4">
              <p className="location-body-text">{introduction1}</p>
              {introduction2 && (
                <p className="location-body-text">{introduction2}</p>
              )}
              {introduction3 && (
                <p className="location-body-text">{introduction3}</p>
              )}
            </div>
            <div className="pt-4">
              <Button asChild size="lg" className="font-semibold w-full sm:w-auto">
                <Link to={ctaLink}>{ctaText}</Link>
              </Button>
            </div>
          </div>
          <div>
            {imageUrl ? (
              <div className="medical-image-container">
                <img
                  src={imageUrl}
                  alt={title}
                  className="w-full h-80 object-cover"
                />
              </div>
            ) : (
              <div className="medical-card h-80 flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/5">
                <div className="text-center">
                  <h3 className="location-card-title mb-2">Professional Care</h3>
                  <p className="location-small-text">Expert medical services</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationHero;
