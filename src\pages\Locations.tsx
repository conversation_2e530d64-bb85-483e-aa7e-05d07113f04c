import { MapPin, Phone, Clock } from 'lucide-react';
import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { generatePageSEO } from '@/lib/seo';

const Locations: React.FC = () => {

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Generate SEO data for locations page
  const locationsSeoData = generatePageSEO('location', {
    title: 'Consulting Locations | Dr <PERSON><PERSON> - <PERSON>eurosurgeon & Spine Surgeon',
    description: 'Find Dr <PERSON>\'s neurosurgery and spine surgery consulting locations across Melbourne including Surrey Hills, Mornington, Bundoora, and more.',
    keywords: ['neurosurgeon locations Melbourne', 'spine surgeon consulting rooms', 'Dr <PERSON><PERSON> locations', 'neurosurgery clinics Melbourne']
  });

  // Location data
  const locations = [
    {
      id: "surrey-hills",
      name: "SURREY HILLS",
      address: "miNEURO Consulting Suites, Suite 4, 619 Canterbury Road, SURREY HILLS VIC 3127",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: true,
      image: "/images/miNEURO-brain-spine-advanced-technology-precision-miniamlly-invasive-neurosurgery-Melbourne-Surrey-Hills-entrance.jpg"
    },
    {
      id: "mornington",
      name: "MORNINGTON",
      address: "Nepean Specialist Centre, 1050 Nepean Highway, MORNINGTON 3931",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/neurosurgery-mornington-specialist-centre-reception-consulting.jpg"
    },
    {
      id: "bundoora",
      name: "BUNDOORA",
      address: "UniHill Consulting Suites, Level 4 Suite 32, 240 Plenty Road, BUNDOORA 3083",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/bundoora-unihill-specialist-consulting-building-240-plenty-road.jpg"
    },
    {
      id: "frankston",
      name: "FRANKSTON",
      address: "Peninsula Private Hospital, Mezzanine Consulting Suites, 525 McClelland Drive, FRANKSTON 3199",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/peninsula-private-hospital-entrance-consulting-ales-aliashkevich-neurosurgeon-spine.jpg"
    },
    {
      id: "langwarrin",
      name: "LANGWARRIN",
      address: "Peninsula Consulting Suites, Suite 3, 35-37 Cranbourne-Frankston Road, LANGWARRIN VIC 3910",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/langwarrin-consulting-peninsula-rheumatology-entrance-neurosurgery.jpg"
    },
    {
      id: "werribee",
      name: "WERRIBEE",
      address: "Consulting Rooms, 297 Princes Highway, WERRIBEE 3030",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/werribee-consulting-rooms-neurosurgery-entrance.jpg"
    },
    {
      id: "heidelberg",
      name: "HEIDELBERG",
      address: "Warringal Private Hospital Consulting Rooms, Level 5, 10 Martin Street, HEIDELBERG 3084",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/heidelberg-consulting-rooms-neurosurgeon-spine-surgeon-10-martin-medical.jpg"
    },
    {
      id: "moonee-ponds",
      name: "MOONEE PONDS",
      address: "Moonee Ponds Specialist Centre, 827 Mt Alexander Road, MOONEE PONDS 3039",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/moonee-ponds-specialist-centre-entrance-scaled.jpg"
    },
    {
      id: "sunbury",
      name: "SUNBURY",
      address: "Lake Imaging, 17-19 Horne Street, SUNBURY 3429",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/sunbury-lake-imaging-consulting-neurosurgery.jpg"
    },
    {
      id: "dandenong",
      name: "DANDENONG",
      address: "Dandenong Neurology & Specialists Group, 136 David Street, DANDENONG 3175",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/dandenong-neurology-specialist-consulting-building-neurosurgeon-spine-surgeon.jpg"
    },
    {
      id: "wantirna",
      name: "WANTIRNA",
      address: "Knox Audiology Specialist Medical Suites, 230 Mountain Highway, WANTIRNA 3152",
      phone: "(03) 9008 4200",
      hours: "Monday - Friday: 8:30 AM - 5:30 PM",
      isPrimary: false,
      image: "/images/wantirna-consulting-rooms-knox-audiology-neurosurgeon-spine-surgeon-entrance.jpg"
    }
  ];

  // Find primary location
  const primaryLocation = locations.find(loc => loc.isPrimary);

  return (
    <StandardPageLayout
      title="Consulting Locations | Dr Ales Aliashkevich - Neurosurgeon & Spine Surgeon"
      subtitle="Find the location most convenient for you across Melbourne"
      pageType="location"
      seoData={locationsSeoData}
      enableParallax={true}
    >
      <div className="flex-1">
        {/* Primary Location Highlight */}
        {primaryLocation && (
          <section className="bg-primary/5 section-padding-lg">
            <div className="container-spacing">
              <div className="text-center section-header-spacing">
                <h2 className="location-section-title text-center">
                  Primary Consulting Location
                </h2>
                <p className="location-body-text max-w-3xl mx-auto">
                  Dr Ales Aliashkevich consults at multiple locations across Melbourne and Victoria, including Surrey Hills, Mornington, Langwarrin, Frankston, Bundoora, and more.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center mt-8">
                <div>
                  <div className="relative w-full rounded-lg overflow-hidden shadow-xl h-64 md:h-80">
                    <SafeImage
                      src={primaryLocation.image || "/images/medical-consulting.jpg"}
                      alt={primaryLocation.name}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>
                <div className="medical-card p-6 md:p-8">
                  <h3 className="location-card-title mb-6">
                    {primaryLocation.name}
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <MapPin className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <p className="location-body-text">{primaryLocation.address}</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <Phone className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <p className="location-body-text">{primaryLocation.phone}</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <Clock className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <p className="location-body-text">{primaryLocation.hours}</p>
                    </div>
                  </div>
                  <div className="mt-6 flex flex-col sm:flex-row gap-4">
                    <Button asChild size="lg">
                      <Link to={`/locations/${primaryLocation.id}`}>View Details</Link>
                    </Button>
                    <Button asChild variant="outline" size="lg">
                      <a href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(primaryLocation.address)}`} target="_blank" rel="noopener noreferrer">
                        Google Maps
                      </a>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* All Locations */}
        <section className="bg-primary/5 section-padding-lg">
          <div className="container-spacing">
            <div className="text-center section-header-spacing">
              <h2 className="location-section-title text-center">
                All Consulting Locations
              </h2>
              <p className="location-body-text max-w-3xl mx-auto">
                Find the location most convenient for you
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {locations?.map(location => (
                <div key={location.id} className="medical-card p-6 hover:shadow-lg transition-shadow duration-300">
                  <h3 className="location-card-title mb-4">
                    {location.name}
                  </h3>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-start gap-3">
                      <MapPin className="h-4 w-4 text-primary mt-1 flex-shrink-0" />
                      <p className="location-small-text">{location.address}</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <Phone className="h-4 w-4 text-primary mt-1 flex-shrink-0" />
                      <p className="location-small-text">{location.phone}</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <Clock className="h-4 w-4 text-primary mt-1 flex-shrink-0" />
                      <p className="location-small-text">{location.hours}</p>
                    </div>
                  </div>
                  <Button asChild variant="outline" className="w-full">
                    <Link to={`/locations/${location.id}`}>View Details</Link>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Appointment CTA */}
        <section className="section-padding-lg">
          <div className="container-spacing">
            <div className="medical-card p-8 md:p-12 text-center max-w-4xl mx-auto">
              <h2 className="location-section-title mb-6">Ready to Address Your Spine or Brain Issue?</h2>
              <p className="location-body-text mb-8 max-w-2xl mx-auto">
                Book a consultation today and learn how Dr. Aliashkevich can improve your quality of life using minimally-invasive techniques.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button asChild size="lg" className="w-full sm:w-auto">
                  <Link to="/appointments">Book an Appointment</Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="w-full sm:w-auto">
                  <Link to="/contact">Contact Us</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </StandardPageLayout>
  );
};

Locations.displayName = 'Locations';

export default Locations;
